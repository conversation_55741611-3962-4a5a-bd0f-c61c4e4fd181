# Open ESP32-S3-Touch-LCD-1.69 project in VS Code with correct ESP-IDF configuration

Write-Host "Opening ESP32-S3-Touch-LCD-1.69 project in VS Code..." -ForegroundColor Green

# Set ESP-IDF environment variables for the correct installation
$env:IDF_PATH = "C:\Users\<USER>\Pictures\ESP-IDF"
$env:IDF_TOOLS_PATH = "C:\Users\<USER>\Pictures\ESP-IDF\tools"
$env:IDF_PYTHON_ENV_PATH = "C:\Users\<USER>\Pictures\ESP-IDF\python_env\idf5.4_py3.11_env"

Write-Host "Environment configured:" -ForegroundColor Yellow
Write-Host "  IDF_PATH: $env:IDF_PATH" -ForegroundColor White
Write-Host "  IDF_TOOLS_PATH: $env:IDF_TOOLS_PATH" -ForegroundColor White
Write-Host "  IDF_PYTHON_ENV_PATH: $env:IDF_PYTHON_ENV_PATH" -ForegroundColor White

# Change to project directory
$projectDir = "01_ESP_IDF_ST7789"
if (Test-Path $projectDir) {
    Set-Location $projectDir
    Write-Host "Changed to project directory: $projectDir" -ForegroundColor Cyan

    # Try to find VS Code executable
    $codeExe = $null
    $possiblePaths = @(
        "${env:LOCALAPPDATA}\Programs\Microsoft VS Code\Code.exe",
        "${env:ProgramFiles}\Microsoft VS Code\Code.exe",
        "${env:ProgramFiles(x86)}\Microsoft VS Code\Code.exe"
    )

    foreach ($path in $possiblePaths) {
        if (Test-Path $path) {
            $codeExe = $path
            break
        }
    }

    if ($codeExe) {
        Write-Host "Opening VS Code..." -ForegroundColor Cyan
        & $codeExe .

        Write-Host ""
        Write-Host "VS Code opened successfully!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Configuration Summary:" -ForegroundColor Yellow
        Write-Host "✓ Project Directory: C:\Users\<USER>\Pictures\ESP-IDF\01_ESP_IDF_ST7789" -ForegroundColor Green
        Write-Host "✓ ESP-IDF Path: C:\Users\<USER>\Pictures\ESP-IDF" -ForegroundColor Green
        Write-Host "✓ Build Directory: C:\Users\<USER>\Pictures\ESP-IDF\01_ESP_IDF_ST7789\build" -ForegroundColor Green
        Write-Host "✓ Target: ESP32-S3" -ForegroundColor Green
        Write-Host ""
        Write-Host "Next Steps:" -ForegroundColor Cyan
        Write-Host "1. The ESP-IDF extension should now recognize the correct installation" -ForegroundColor White
        Write-Host "2. Click the ESP-IDF icon in the left sidebar (Espressif logo)" -ForegroundColor White
        Write-Host "3. Click 'Build' to compile the touch calibration project" -ForegroundColor White
        Write-Host "4. Click 'Flash' to upload to your ESP32-S3-Touch-LCD-1.69 board" -ForegroundColor White
        Write-Host "5. Click 'Monitor' to see the clean touch coordinate output" -ForegroundColor White
    } else {
        Write-Host "Error: VS Code executable not found!" -ForegroundColor Red
        Write-Host "Please install VS Code or add it to your PATH" -ForegroundColor Yellow
        Write-Host "You can manually navigate to: $((Get-Location).Path)" -ForegroundColor White
    }

} else {
    Write-Host "Error: Project directory not found: $projectDir" -ForegroundColor Red
    Write-Host "Make sure you're running this script from the ESP-IDF root directory" -ForegroundColor Yellow
}
