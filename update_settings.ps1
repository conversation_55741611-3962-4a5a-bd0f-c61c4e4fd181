﻿# PowerShell script to update VS Code settings.json for ESP32-S3-Touch-LCD-1.69 project
Write-Host "Updating VS Code settings.json for ESP32-S3-Touch-LCD-1.69 project..." -ForegroundColor Green

$settingsFile = "01_ESP_IDF_ST7789\.vscode\settings.json"

if (Test-Path $settingsFile) {
    Write-Host "Found settings file: $settingsFile" -ForegroundColor Yellow
    
    # Read the current content
    $content = Get-Content $settingsFile -Raw
    
    # Create backup
    $backupFile = "$settingsFile.backup"
    Copy-Item $settingsFile $backupFile
    Write-Host "Created backup: $backupFile" -ForegroundColor Cyan
    
    # Replace the portable ESP-IDF paths with standard Espressif installation paths
    Write-Host "Updating ESP-IDF paths..." -ForegroundColor Yellow
    
    # Update includePath - need to escape the backslashes properly for JSON
    $content = $content -replace 'C:\\\\Users\\\\<USER>\\\\Pictures\\\\ESP-IDF\\\\components\\\\\*\*', 'C:\\\\Espressif\\\\frameworks\\\\esp-idf-v5.4.1\\\\components\\\\**'
    
    # Update browse.path
    $content = $content -replace 'C:\\\\Users\\\\<USER>\\\\Pictures\\\\ESP-IDF\\\\components', 'C:\\\\Espressif\\\\frameworks\\\\esp-idf-v5.4.1\\\\components'
    
    # Write the updated content back to the file
    $content | Set-Content $settingsFile -NoNewline
    
    Write-Host "Successfully updated VS Code settings!" -ForegroundColor Green
    Write-Host "Changes made:" -ForegroundColor Cyan
    Write-Host "  - Updated includePath to use Espressif installation" -ForegroundColor White
    Write-Host "  - Updated browse.path to use Espressif installation" -ForegroundColor White
    
} else {
    Write-Host "Error: Settings file not found at $settingsFile" -ForegroundColor Red
}
