# PowerShell script to update VS Code settings.json for ESP32-S3-Touch-LCD-1.69 project
# This script updates the ESP-IDF paths to use the correct Espressif installation

Write-Host "Updating VS Code settings.json for ESP32-S3-Touch-LCD-1.69 project..." -ForegroundColor Green

$settingsFile = "01_ESP_IDF_ST7789\.vscode\settings.json"

if (Test-Path $settingsFile) {
    Write-Host "Found settings file: $settingsFile" -ForegroundColor Yellow
    
    # Read the current content
    $content = Get-Content $settingsFile -Raw
    
    # Create backup
    $backupFile = "$settingsFile.backup"
    Copy-Item $settingsFile $backupFile
    Write-Host "Created backup: $backupFile" -ForegroundColor Cyan
    
    # Replace the portable ESP-IDF paths with standard Espressif installation paths
    Write-Host "Updating ESP-IDF paths..." -ForegroundColor Yellow
    
    # Update includePath
    $content = $content -replace 'C:\\\\Users\\\\<USER>\\\\Pictures\\\\ESP-IDF\\\\components\\\\\*\*', 'C:\\\\Espressif\\\\frameworks\\\\esp-idf-v5.4.1\\\\components\\\\**'
    
    # Update browse.path
    $content = $content -replace 'C:\\\\Users\\\\<USER>\\\\Pictures\\\\ESP-IDF\\\\components', 'C:\\\\Espressif\\\\frameworks\\\\esp-idf-v5.4.1\\\\components'
    
    # Write the updated content back to the file
    $content | Set-Content $settingsFile -NoNewline
    
    Write-Host "Successfully updated VS Code settings!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Changes made:" -ForegroundColor Cyan
    Write-Host "  - Updated C_Cpp.default.includePath to use C:\Espressif\frameworks\esp-idf-v5.4.1\components\**" -ForegroundColor White
    Write-Host "  - Updated C_Cpp.default.browse.path to use C:\Espressif\frameworks\esp-idf-v5.4.1\components" -ForegroundColor White
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Open VS Code from the project directory: cd 01_ESP_IDF_ST7789 && code ." -ForegroundColor White
    Write-Host "2. The ESP-IDF extension should now use the correct installation path" -ForegroundColor White
    Write-Host "3. Try building the project using the ESP-IDF extension Build button" -ForegroundColor White
    
} else {
    Write-Host "Error: Settings file not found at $settingsFile" -ForegroundColor Red
    Write-Host "Make sure you're running this script from the ESP-IDF root directory" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Script completed!" -ForegroundColor Green
